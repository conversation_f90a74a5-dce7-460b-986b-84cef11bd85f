<!DOCTYPE html>
<html>

<head>
    <title>Student Grading Result</title>

    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <style type="text/css" media="screen">
        /* Base styles */
        html {
            font-family: "DejaVu Sans", sans-serif; /* Consistent font */
            line-height: 1.15;
            margin: 0;
        }

        body {
            font-weight: 400;
            line-height: 1.5;
            color: #212529;
            text-align: left;
            background-color: #fff;
            font-size: 12px; /* Slightly smaller base font for print */
            margin: 20pt 30pt; /* Adjust margins */
        }

        /* Typography */
        h4, .h4 {
            margin-top: 0;
            margin-bottom: 0.5rem;
            font-weight: 500;
            line-height: 1.2;
            font-size: 1.3rem; /* Adjust heading size */
        }

        p {
            margin-top: 0;
            margin-bottom: 0.8rem; /* Adjust paragraph spacing */
            line-height: 1.3;
        }

        strong {
            font-weight: bolder;
        }

        ul {
            padding-left: 20px;
            margin-top: 0.5rem;
            margin-bottom: 0.5rem;
        }
        li {
            margin-bottom: 0.3rem;
        }

        /* Images */
        img {
            vertical-align: middle;
            border-style: none;
            max-width: 100%; /* Ensure images don't overflow */
            height: auto;
        }

        /* Tables */
        table {
            border-collapse: collapse;
            width: 100%;
        }

        th, td {
            text-align: inherit;
            padding: 0.5rem; /* Standardized padding */
            vertical-align: top;
            border: 1px solid #dee2e6; /* Default border for all cells */
            line-height: 1.3;
        }

        thead th {
            font-weight: bold;
            background-color: #e9ecef; /* Lighter header background */
            border-bottom-width: 2px;
            text-align: center;
            vertical-align: middle;
        }

        /* Specific table adjustments */
        .table-header td { /* Table for logo/header */
            border: none; /* Remove borders in the header table */
            padding: 0;
            vertical-align: middle;
        }

        .tg { /* Main results table */
            margin-top: 1.5rem;
            margin-bottom: 1rem;
            font-size: 11px; /* Smaller font for the results table if needed */
        }

        .tg th {
            background-color: #3B71CA; /* Restore original header color */
            color: #ffffff; /* White text for header */
            font-weight: bold;
            text-align: center;
        }

        .tg td {
            padding: 4px 5px; /* Fine-tune padding */
        }

        .tg .tg-cly1, .tg .tg-zr06 {
            text-align: left;
            vertical-align: middle;
        }

        /* Utilities */
        .mt-5 {
            margin-top: 2rem !important; /* Adjust spacing */
        }
        .mb-0 { margin-bottom: 0 !important; }
        .pl-0 { padding-left: 0 !important; }
        .text-right { text-align: right !important; }
        .text-center { text-align: center !important; }
        .text-uppercase { text-transform: uppercase !important; }
        .border-0 { border: none !important; }
        .cool-gray { color: #3B71CA; }
        .red { color: #DC4C64; }

        /* Header specific styles */
        .header-logo {
            text-align: left;
        }
        .header-info {
            text-align: center;
        }
        .header-info h4 {
            font-size: 1.1rem; /* Smaller heading in header */
            margin-bottom: 0.3rem;
        }
        .header-info p {
            margin-bottom: 0.2rem;
            font-size: 10px; /* Smaller text in header */
            line-height: 1.2;
        }

        /* Footer text */
        .footer-text {
            margin-top: 2rem;
            font-size: 11px;
        }

        /* Print specific styles (optional but good practice) */
        @media print {
            body {
                margin: 15pt 25pt; /* Slightly tighter margins for print */
                font-size: 11pt; /* Ensure readable font size in points */
            }
            thead {
                display: table-header-group; /* Ensure header repeats on each page */
            }
            tr {
                page-break-inside: avoid; /* Try to keep rows together */
            }
            .no-print {
                display: none; /* Class to hide elements in print */
            }
            /* Ensure background colors print if needed (browser settings often override this) */
            .tg th {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>

<body>

    <table class="table-header"> <!-- Use specific class for header table -->
        <tbody>
            <tr>
                <td class="header-logo" width="25%"> <!-- Adjust width if needed -->
                    <img src="https://www.institut-imsaa.com/image/logo/logo2-removebg-preview.png" alt="logo IMSAA"
                        height="80"> <!-- Adjust height if needed -->
                </td>
                <td class="header-info"> <!-- Use specific class -->
                    <h4 class="text-uppercase cool-gray mb-0"> <!-- Removed strong, adjusted margin -->
                        <span class="red">I</span>NSTITUT DE <span class="red">M</span>ANAGEMENT ET
                            DES <span class="red">S</span>CIENCES <span class="red">A</span>PPLIQUEES <br>D'<span
                                class="red">A</span>NTSIRANANA
                    </h4>
                    <p class="mb-0">DIRECTION GENERALE</p>
                    <p class="mb-0">DIRECTION DES ETUDES DE LA RECHERCHE ET DE L’ASSURANCE QUALITE</p>
                    <p class="mb-0">Domaine : <?php echo e($parcours->first()->mention->domaine->nom); ?></p>
                </td>
            </tr>
        </tbody>
    </table>

    <div class="text-center" style="margin-top: 1rem; margin-bottom: 1rem;"> <!-- Wrap title section -->
        <?php if(!empty($notes)): ?>
            <strong style="font-size: 1.1rem;">Liste des résultats en <?php echo e($niveau->nom); ?> par ordre de mérite</strong>
            <p style="margin-top: 0.5rem; margin-bottom: 0.2rem;">Semestres :</p>
            <ul style="list-style: none; padding-left: 0;">
                <?php $__currentLoopData = $semestres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $semestre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li style="display: inline-block; margin-right: 10px;"><?php echo e($semestre->nom); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <p style="margin-top: 0.5rem; margin-bottom: 0.2rem;">Parcours :</p>
            <ul style="list-style: none; padding-left: 0;">
                <?php $__currentLoopData = $parcours; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $parcour): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                     <li style="display: inline-block; margin-right: 10px;"><?php echo e($parcour->nom); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <p style="margin-top: 0.5rem; margin-bottom: 0.5rem;">Année universitaire : <?php echo e($annee->nom); ?></p>
        <?php else: ?>
            <p>Pas de résultat disponible.</p>
        <?php endif; ?>
        
    </div>

    <table class="tg">
        <thead>
            <tr>
                <th class="tg-cly1" width="10%">RANG</th> <!-- Removed span, added width -->
                <th class="tg-cly1" width="60%">NOM ET PRENOMS</th> <!-- Removed span, added width -->
                <th class="tg-cly1" width="30%">MENTION</th> <!-- Removed span, added width -->
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $notes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $result): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr> <!-- Removed inline style -->
                    <td class="tg-zr06 text-center"><?php echo e($loop->iteration); ?></td> <!-- Removed span, added text-center -->
                    <td class="tg-zr06">
                        <?php echo e($result["nom"]); ?> <?php echo e($result["prenom"]); ?> <!-- Removed span -->
                    </td>
                    <td class="tg-zr06 text-center"> <!-- Removed span, added text-center -->
                        <?php echo e($result["mention"]); ?>

                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="3" class="text-center"> <!-- Adjusted colspan -->
                        <p class="mb-0">
                            Impossible de générer. Pas encore de note ajoutée !
                        </p>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <p class="footer-text"> <!-- Use specific class -->
        Arrêté la présente liste au nombre de <?php echo e(count($notes)); ?> étudiant(s).
    </p>
    <p class="footer-text"> <!-- Use specific class -->
        Fait à Antsiranana, le <?php echo e(\Carbon\Carbon::now()->locale('fr')->isoFormat('DD MMMM YYYY')); ?>

    </p>
    <!-- Optional: Add signature lines if needed -->
    <table style="width: 100%; margin-top: 3rem; border: none;">
        <tr style="border: none;">
            <td style="width: 50%; text-align: center; border: none;"></td>
            <td style="width: 50%; text-align: center; border: none;">La Direction Générale</td>
        </tr>
    </table>

</body>

</html>
<?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/pdf/resultat.blade.php ENDPATH**/ ?>