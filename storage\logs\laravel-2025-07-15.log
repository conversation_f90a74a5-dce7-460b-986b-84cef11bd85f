[2025-07-15 09:58:47] local.INFO: Detected N+1 Query  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 9
Call-Stack:
#17 \app\Http\Livewire\Result.php:180
#18 \app\Http\Livewire\Result.php:151
#19 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#20 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#23 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#24 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#25 \vendor\livewire\livewire\src\LifecycleManager.php:89
#26 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#27 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#28 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#29 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#30 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#31 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#32 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#33 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#35 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#40 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
#48 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:776
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:740
  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 27
Call-Stack:
#22 \app\Http\Livewire\Result.php:180
#23 \app\Http\Livewire\Result.php:151
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#25 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#28 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#29 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#30 \vendor\livewire\livewire\src\LifecycleManager.php:89
#31 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#32 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#33 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#34 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#36 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#38 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 14
Call-Stack:
#17 \app\Http\Livewire\Result.php:180
#18 \app\Http\Livewire\Result.php:151
#19 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#20 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#23 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#24 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#25 \vendor\livewire\livewire\src\LifecycleManager.php:89
#26 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#27 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#28 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#29 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#30 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#31 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#32 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#33 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#35 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#40 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
#48 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:776
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:740
  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 42
Call-Stack:
#22 \app\Http\Livewire\Result.php:180
#23 \app\Http\Livewire\Result.php:151
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#25 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#28 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#29 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#30 \vendor\livewire\livewire\src\LifecycleManager.php:89
#31 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#32 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#33 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#34 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#36 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#38 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 4
Call-Stack:
#17 \app\Http\Livewire\Result.php:180
#18 \app\Http\Livewire\Result.php:151
#19 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#20 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#23 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#24 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#25 \vendor\livewire\livewire\src\LifecycleManager.php:89
#26 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#27 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#28 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#29 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#30 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#31 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#32 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#33 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#35 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#40 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
#48 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:776
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:740
  
[2025-07-15 09:58:47] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 12
Call-Stack:
#22 \app\Http\Livewire\Result.php:180
#23 \app\Http\Livewire\Result.php:151
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#25 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#28 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#29 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#30 \vendor\livewire\livewire\src\LifecycleManager.php:89
#31 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#32 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#33 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#34 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#36 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#38 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
[2025-07-15 11:54:02] local.INFO: Detected N+1 Query  
[2025-07-15 11:54:03] local.INFO: Model: App\Models\Ue
Relation: App\Models\Matiere
Num-Called: 14
Call-Stack:
#17 \app\Http\Livewire\Result.php:180
#18 \app\Http\Livewire\Result.php:151
#19 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#20 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#21 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#22 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#23 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#24 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#25 \vendor\livewire\livewire\src\LifecycleManager.php:89
#26 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#27 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#28 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#29 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#30 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#31 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#32 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#33 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#34 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#35 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#36 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#37 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#38 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#39 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#40 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
#45 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:116
#47 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:797
#48 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:776
#49 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:740
  
[2025-07-15 11:54:03] local.INFO: Model: App\Models\Matiere
Relation: App\Models\Note
Num-Called: 42
Call-Stack:
#22 \app\Http\Livewire\Result.php:180
#23 \app\Http\Livewire\Result.php:151
#24 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:36
#25 \vendor\laravel\framework\src\Illuminate\Container\Util.php:41
#26 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:93
#27 \vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php:35
#28 \vendor\livewire\livewire\src\ComponentConcerns\HandlesActions.php:149
#29 \vendor\livewire\livewire\src\HydrationMiddleware\PerformActionCalls.php:36
#30 \vendor\livewire\livewire\src\LifecycleManager.php:89
#31 \vendor\livewire\livewire\src\Connection\ConnectionHandler.php:13
#32 \vendor\livewire\livewire\src\Controllers\HttpConnectionHandler.php:18
#33 \vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php:46
#34 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:259
#35 \vendor\laravel\framework\src\Illuminate\Routing\Route.php:205
#36 \vendor\laravel\framework\src\Illuminate\Routing\Router.php:798
#37 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:141
#38 \vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php:50
#39 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#40 \vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php:78
#41 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#42 \vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php:49
#43 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#44 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:121
#45 \vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php:64
#46 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#47 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php:37
#48 \vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php:180
#49 \vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php:67
  
