{"__meta": {"id": "Xf038ecaf9f355819e5309099dae2b276", "datetime": "2025-07-15 11:54:03", "utime": **********.042921, "method": "POST", "uri": "/livewire/message/result", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[11:54:03] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.038457, "collector": "log"}, {"message": "[11:54:03] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 14\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Result.php:180\r\n#18 \\app\\Http\\Livewire\\Result.php:151\r\n#19 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#20 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#23 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#24 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#25 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#26 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#27 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#28 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:776\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:740\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.040735, "collector": "log"}, {"message": "[11:54:03] LOG.info: Model: App\\Models\\Matiere\r\nRelation: App\\Models\\Note\r\nNum-Called: 42\r\nCall-Stack:\r\n#22 \\app\\Http\\Livewire\\Result.php:180\r\n#23 \\app\\Http\\Livewire\\Result.php:151\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#25 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#26 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#27 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#29 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#30 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#31 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#32 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.041027, "collector": "log"}]}, "time": {"start": 1752569633.143318, "end": **********.04296, "duration": 9.899641990661621, "duration_str": "9.9s", "measures": [{"label": "Booting", "start": 1752569633.143318, "relative_start": 0, "end": 1752569634.148789, "relative_end": 1752569634.148789, "duration": 1.0054709911346436, "duration_str": "1.01s", "params": [], "collector": null}, {"label": "Application", "start": 1752569634.149929, "relative_start": 1.0066111087799072, "end": **********.042963, "relative_end": 3.0994415283203125e-06, "duration": 8.893033981323242, "duration_str": "8.89s", "params": [], "collector": null}]}, "memory": {"peak_usage": 28208120, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 2, "templates": [{"name": "livewire.deraq.resultat.index (\\resources\\views\\livewire\\deraq\\resultat\\index.blade.php)", "param_count": 14, "params": ["parcours", "niveaux", "semestres", "annees", "livewireLayout", "errors", "_instance", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/index.blade.php&line=0"}, {"name": "livewire.deraq.resultat.liste (\\resources\\views\\livewire\\deraq\\resultat\\liste.blade.php)", "param_count": 16, "params": ["__env", "app", "errors", "_instance", "parcours", "niveaux", "semestres", "annees", "livewireLayout", "newResults", "notes", "showResults", "current_parcours", "current_niveau", "current_semestres", "current_annee"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/resultat/liste.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 76, "nb_failed_statements": 0, "accumulated_duration": 3.4507600000000003, "accumulated_duration_str": "3.45s", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00847, "duration_str": "8.47ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 0.245}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.02711, "duration_str": "27.11ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:122", "connection": "imsaaapp", "start_percent": 0.245, "width_percent": 0.786}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00875, "duration_str": "8.75ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:125", "connection": "imsaaapp", "start_percent": 1.031, "width_percent": 0.254}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 128}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Result.php:128", "connection": "imsaaapp", "start_percent": 1.285, "width_percent": 0.021}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 131}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:131", "connection": "imsaaapp", "start_percent": 1.306, "width_percent": 0.03}, {"sql": "select `id`, `nom`, `prenom` from `users` where exists (select * from `inscription_students` where `users`.`id` = `inscription_students`.`user_id` and `niveau_id` = '3' and `annee_universitaire_id` = '5' and `parcour_id` in ('1') and `inscription_students`.`deleted_at` is null) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "5", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 148}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.46364999999999995, "duration_str": "464ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:148", "connection": "imsaaapp", "start_percent": 1.335, "width_percent": 13.436}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 188 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "188", "1", "188", "2", "188", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08654, "duration_str": "86.54ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 14.772, "width_percent": 2.508}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05885, "duration_str": "58.85ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 17.279, "width_percent": 1.705}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05066, "duration_str": "50.66ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 18.985, "width_percent": 1.468}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00151, "duration_str": "1.51ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 20.453, "width_percent": 0.044}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 188 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "188"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06197, "duration_str": "61.97ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 20.497, "width_percent": 1.796}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 189 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "189", "1", "189", "2", "189", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023780000000000003, "duration_str": "23.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 22.292, "width_percent": 0.689}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04154, "duration_str": "41.54ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 22.982, "width_percent": 1.204}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.056600000000000004, "duration_str": "56.6ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 24.185, "width_percent": 1.64}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0343, "duration_str": "34.3ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 25.826, "width_percent": 0.994}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 189 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "189"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01924, "duration_str": "19.24ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 26.82, "width_percent": 0.558}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 192 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "192", "1", "192", "2", "192", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.053770000000000005, "duration_str": "53.77ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 27.377, "width_percent": 1.558}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00762, "duration_str": "7.62ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 28.935, "width_percent": 0.221}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00601, "duration_str": "6.01ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 29.156, "width_percent": 0.174}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04188, "duration_str": "41.88ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 29.33, "width_percent": 1.214}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 192 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "192"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01838, "duration_str": "18.38ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 30.544, "width_percent": 0.533}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 193 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "193", "1", "193", "2", "193", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023629999999999998, "duration_str": "23.63ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 31.077, "width_percent": 0.685}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04504, "duration_str": "45.04ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 31.761, "width_percent": 1.305}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0205, "duration_str": "20.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 33.067, "width_percent": 0.594}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01548, "duration_str": "15.48ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 33.661, "width_percent": 0.449}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 193 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "193"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05004, "duration_str": "50.04ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 34.109, "width_percent": 1.45}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 194 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "194", "1", "194", "2", "194", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01918, "duration_str": "19.18ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 35.559, "width_percent": 0.556}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02527, "duration_str": "25.27ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 36.115, "width_percent": 0.732}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0385, "duration_str": "38.5ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 36.848, "width_percent": 1.116}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05908, "duration_str": "59.08ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 37.963, "width_percent": 1.712}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 194 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "194"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03925, "duration_str": "39.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 39.675, "width_percent": 1.137}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 195 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "195", "1", "195", "2", "195", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.09109, "duration_str": "91.09ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 40.813, "width_percent": 2.64}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08003, "duration_str": "80.03ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 43.452, "width_percent": 2.319}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.1443, "duration_str": "144ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 45.772, "width_percent": 4.182}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07242, "duration_str": "72.42ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 49.953, "width_percent": 2.099}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 195 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "195"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01669, "duration_str": "16.69ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 52.052, "width_percent": 0.484}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 198 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "198", "1", "198", "2", "198", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04008, "duration_str": "40.08ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 52.536, "width_percent": 1.161}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.023170000000000003, "duration_str": "23.17ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 53.697, "width_percent": 0.671}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06061, "duration_str": "60.61ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 54.369, "width_percent": 1.756}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.019190000000000002, "duration_str": "19.19ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 56.125, "width_percent": 0.556}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 198 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "198"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03698, "duration_str": "36.98ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 56.681, "width_percent": 1.072}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 199 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "199", "1", "199", "2", "199", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05196, "duration_str": "51.96ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 57.753, "width_percent": 1.506}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03475, "duration_str": "34.75ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 59.259, "width_percent": 1.007}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01998, "duration_str": "19.98ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 60.266, "width_percent": 0.579}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02311, "duration_str": "23.11ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 60.845, "width_percent": 0.67}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 199 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "199"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03578, "duration_str": "35.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 61.514, "width_percent": 1.037}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 200 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "200", "1", "200", "2", "200", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0217, "duration_str": "21.7ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 62.551, "width_percent": 0.629}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03782, "duration_str": "37.82ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 63.18, "width_percent": 1.096}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.045079999999999995, "duration_str": "45.08ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 64.276, "width_percent": 1.306}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02479, "duration_str": "24.79ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 65.582, "width_percent": 0.718}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 200 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "200"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.03329, "duration_str": "33.29ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 66.301, "width_percent": 0.965}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 201 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "201", "1", "201", "2", "201", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04086, "duration_str": "40.86ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 67.265, "width_percent": 1.184}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04936, "duration_str": "49.36ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 68.45, "width_percent": 1.43}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08244, "duration_str": "82.44ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 69.88, "width_percent": 2.389}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02794, "duration_str": "27.94ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 72.269, "width_percent": 0.81}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 201 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "201"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0291, "duration_str": "29.1ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 73.079, "width_percent": 0.843}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 203 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "203", "1", "203", "2", "203", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.07289, "duration_str": "72.89ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 73.922, "width_percent": 2.112}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02984, "duration_str": "29.84ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 76.034, "width_percent": 0.865}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.044590000000000005, "duration_str": "44.59ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 76.899, "width_percent": 1.292}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01515, "duration_str": "15.15ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 78.191, "width_percent": 0.439}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 203 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "203"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.1657, "duration_str": "166ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 78.63, "width_percent": 4.802}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 204 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "204", "1", "204", "2", "204", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.05001, "duration_str": "50.01ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 83.432, "width_percent": 1.449}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.017, "duration_str": "17ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 84.881, "width_percent": 0.493}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.024149999999999998, "duration_str": "24.15ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 85.374, "width_percent": 0.7}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.019960000000000002, "duration_str": "19.96ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 86.074, "width_percent": 0.578}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 204 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "204"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06106, "duration_str": "61.06ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 86.652, "width_percent": 1.769}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 207 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "207", "1", "207", "2", "207", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06978, "duration_str": "69.78ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 88.422, "width_percent": 2.022}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.030019999999999998, "duration_str": "30.02ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 90.444, "width_percent": 0.87}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.08595, "duration_str": "85.95ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 91.314, "width_percent": 2.491}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.06193, "duration_str": "61.93ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 93.805, "width_percent": 1.795}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 207 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "207"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.04722, "duration_str": "47.22ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 95.599, "width_percent": 1.368}, {"sql": "select * from `ues` where `semestre_id` in ('5') and `annee_universitaire_id` = '5' and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 1 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 2 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and exists (select * from `matieres` where `ues`.`id` = `matieres`.`ue_id` and exists (select * from `notes` where `matieres`.`id` = `notes`.`matiere_id` and `user_id` = 227 and `type_note_id` = 3 and `notes`.`deleted_at` is null) and `matieres`.`deleted_at` is null) and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "5", "227", "1", "227", "2", "227", "3"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.039549999999999995, "duration_str": "39.55ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 96.968, "width_percent": 1.146}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (383, 384, 385) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00982, "duration_str": "9.82ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 98.114, "width_percent": 0.285}, {"sql": "select * from `notes` where `type_note_id` = 1 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.01525, "duration_str": "15.25ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 98.398, "width_percent": 0.442}, {"sql": "select * from `notes` where `type_note_id` = 2 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.02779, "duration_str": "27.79ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 98.84, "width_percent": 0.805}, {"sql": "select * from `notes` where `type_note_id` = 3 and `notes`.`matiere_id` in (640, 641, 642, 643, 644) and `user_id` = 227 and `notes`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "227"], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Result.php", "line": 151}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.012230000000000001, "duration_str": "12.23ms", "stmt_id": "\\app\\Http\\Livewire\\Result.php:180", "connection": "imsaaapp", "start_percent": 99.646, "width_percent": 0.354}]}, "models": {"data": {"App\\Models\\Note": 224, "App\\Models\\Matiere": 70, "App\\Models\\Ue": 42, "App\\Models\\AnneeUniversitaire": 6, "App\\Models\\Semestre": 10, "App\\Models\\Niveau": 5, "App\\Models\\Parcour": 24, "App\\Models\\User": 15}, "count": 396}, "livewire": {"data": {"result #BMXcBqTM84503PzVGsJu": "array:5 [\n  \"data\" => array:7 [\n    \"newResults\" => array:4 [\n      \"parcour_id\" => array:1 [\n        0 => \"1\"\n      ]\n      \"niveau_id\" => \"3\"\n      \"semestre_id\" => array:1 [\n        0 => \"5\"\n      ]\n      \"annee_universitaire_id\" => \"5\"\n    ]\n    \"notes\" => array:14 [\n      0 => array:6 [\n        \"nom\" => \"TSARALAZA \"\n        \"prenom\" => \"Vonindraozy Anouchka Norah\"\n        \"moy_raw\" => 15.083333333333\n        \"moy\" => \"15.08\"\n        \"mention\" => \"Bien\"\n        \"rang\" => 1\n      ]\n      1 => array:6 [\n        \"nom\" => \"LOVA\"\n        \"prenom\" => \"Faniva Tsimaholy\"\n        \"moy_raw\" => 12.916666666667\n        \"moy\" => \"12.92\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 2\n      ]\n      2 => array:6 [\n        \"nom\" => \"RANARY \"\n        \"prenom\" => \"<PERSON><PERSON>\"\n        \"moy_raw\" => 12.833333333333\n        \"moy\" => \"12.83\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 3\n      ]\n      3 => array:6 [\n        \"nom\" => \"RAHERIMANANA\"\n        \"prenom\" => \"Laurencia\"\n        \"moy_raw\" => 12.208333333333\n        \"moy\" => \"12.21\"\n        \"mention\" => \"Assez-bien\"\n        \"rang\" => 4\n      ]\n      4 => array:6 [\n        \"nom\" => \"RAVELOMIRANA\"\n        \"prenom\" => \"Lauriana Sylvanna\"\n        \"moy_raw\" => 11.458333333333\n        \"moy\" => \"11.46\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 5\n      ]\n      5 => array:6 [\n        \"nom\" => \"SOA \"\n        \"prenom\" => \"Philipine Causta\"\n        \"moy_raw\" => 10.875\n        \"moy\" => \"10.88\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 6\n      ]\n      6 => array:6 [\n        \"nom\" => \"RAZAFIMBELO\"\n        \"prenom\" => \"Marie Stella Philippe\"\n        \"moy_raw\" => 10.833333333333\n        \"moy\" => \"10.83\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 7\n      ]\n      7 => array:6 [\n        \"nom\" => \"RAZAFY \"\n        \"prenom\" => \"Marie Antoniesca\"\n        \"moy_raw\" => 10.541666666667\n        \"moy\" => \"10.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 8\n      ]\n      8 => array:6 [\n        \"nom\" => \"TREFINDRAZANA \"\n        \"prenom\" => \"Ricina\"\n        \"moy_raw\" => 9.7083333333333\n        \"moy\" => \"9.71\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 9\n      ]\n      9 => array:6 [\n        \"nom\" => \"LASALMONIE\"\n        \"prenom\" => \"Joana Richina\"\n        \"moy_raw\" => 9.5\n        \"moy\" => \"9.50\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 10\n      ]\n      10 => array:6 [\n        \"nom\" => \"MANOROSOA\"\n        \"prenom\" => \"Aboudou Sandra\"\n        \"moy_raw\" => 8.375\n        \"moy\" => \"8.38\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 11\n      ]\n      11 => array:6 [\n        \"nom\" => \"SAGNITRY\"\n        \"prenom\" => \"Rachida Adrianah\"\n        \"moy_raw\" => 8.2083333333333\n        \"moy\" => \"8.21\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 12\n      ]\n      12 => array:6 [\n        \"nom\" => \"IASILANY\"\n        \"prenom\" => \"Prisco\"\n        \"moy_raw\" => 7.5416666666667\n        \"moy\" => \"7.54\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 13\n      ]\n      13 => array:6 [\n        \"nom\" => \"HOLIFARA\"\n        \"prenom\" => \"Andrianina Jaura\"\n        \"moy_raw\" => 3.3333333333333\n        \"moy\" => \"3.33\"\n        \"mention\" => \"Passable\"\n        \"rang\" => 14\n      ]\n    ]\n    \"showResults\" => true\n    \"current_parcours\" => Illuminate\\Database\\Eloquent\\Collection {#2803\n      #items: array:1 [\n        0 => App\\Models\\Parcour {#1425\n          #connection: \"mysql\"\n          #table: \"parcours\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #original: array:7 [\n            \"id\" => 1\n            \"sigle\" => \"THR\"\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"mention_id\" => 4\n            \"created_at\" => null\n            \"updated_at\" => null\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:3 [\n            0 => \"nom\"\n            1 => \"sigle\"\n            2 => \"mention_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_niveau\" => App\\Models\\Niveau {#1765\n      #connection: \"mysql\"\n      #table: \"niveaux\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #original: array:4 [\n        \"id\" => 3\n        \"nom\" => \"3ème année\"\n        \"sigle\" => \"L3\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: []\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n    \"current_semestres\" => Illuminate\\Database\\Eloquent\\Collection {#2979\n      #items: array:1 [\n        0 => App\\Models\\Semestre {#1782\n          #connection: \"mysql\"\n          #table: \"semestres\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #original: array:4 [\n            \"id\" => 5\n            \"nom\" => \"Semestre 5\"\n            \"niveau_id\" => 3\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: false\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"nom\"\n            1 => \"niveau_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n        }\n      ]\n      #escapeWhenCastingToString: false\n    }\n    \"current_annee\" => App\\Models\\AnneeUniversitaire {#1797\n      #connection: \"mysql\"\n      #table: \"annee_universitaires\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #original: array:3 [\n        \"id\" => 5\n        \"nom\" => \"2023/2024\"\n        \"deleted_at\" => null\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: false\n      #hidden: []\n      #visible: []\n      #fillable: array:1 [\n        0 => \"nom\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n    }\n  ]\n  \"name\" => \"result\"\n  \"view\" => \"livewire.deraq.resultat.index\"\n  \"component\" => \"App\\Http\\Livewire\\Result\"\n  \"id\" => \"BMXcBqTM84503PzVGsJu\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/resultat\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752562641\n]"}, "request": {"path_info": "/livewire/message/result", "status_code": "<pre class=sf-dump id=sf-dump-1551287669 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1551287669\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1560876517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1560876517\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-315125956 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">BMXcBqTM84503PzVGsJu</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">result</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"21 characters\">pedagogiques/resultat</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">e4f816ae</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>newResults</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>parcour_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>niveau_id</span>\" => \"<span class=sf-dump-str>3</span>\"\n        \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>5</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>annee_universitaire_id</span>\" => <span class=sf-dump-num>0</span>\n      </samp>]\n      \"<span class=sf-dump-key>notes</span>\" => []\n      \"<span class=sf-dump-key>showResults</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>current_parcours</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_niveau</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_semestres</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>current_annee</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">580a8b2b34e86af7ee33cd2f9ef481b9f154f86c7eeaa0f5561c7bb574b26a1b</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">syncInput</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">xh0k</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"33 characters\">newResults.annee_universitaire_id</span>\"\n        \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str>5</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">r2yu</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">generateResults</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-315125956\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1788810094 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">712</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRVM2RjUC9tM0dlbDVXb1FGRkZPUXc9PSIsInZhbHVlIjoiT0d1OHBlOHR3dFloTFYxTURlUWdnMlB6NG1GZ0R3d1paN2xGbjlvVVd4YzU2YmZoZmtZUXJadkV6dFlyQmsvZEVEM2k1Y2pjaEs5aTc3NUoyRFZIMGpITkdsbUY3OU1wYXVtTkgrSTdnaWFRUkFYb0d5VEdTRkd3RlBPQXphU3IiLCJtYWMiOiI4YzkxYzIxOTE1ZjRhNzgzNWU5MGZmZWZhMzI1YzJjZDljNjgyMzZlNDVmNTY3YmIwZDMwOTliNzMyOTRiZGQxIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ind1RmZHWDhTQWFnVkg1enVFc0phc3c9PSIsInZhbHVlIjoidXkxL2RTMVJhYXNtaVhSNml2N2NnbUllUVdhU3dBN0xqdUoyWU5hcjFsL2tFTzNudWx2ZnpvRFpxVmN5OFB3M1cxZTgrUzdtOCtIVHd2UWlkNnpXYU5kSVh1c3VZcTB6WGJlSG1zZDZUUStwWnNmbnJxdFZMVTVPOStUd2JPaXUiLCJtYWMiOiIxZGVlODliZTYwZWNiOTY5ZjM5MDMxMTNiYzlkN2ZmZDBmYjlkNjEzY2ZkZjUyYzU5N2U5ZDZhODI0M2U0NjQ0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1788810094\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-171935784 data-indent-pad=\"  \"><span class=sf-dump-note>array:36</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">64240</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"24 characters\">/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"34 characters\">/index.php/livewire/message/result</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">712</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">712</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlRVM2RjUC9tM0dlbDVXb1FGRkZPUXc9PSIsInZhbHVlIjoiT0d1OHBlOHR3dFloTFYxTURlUWdnMlB6NG1GZ0R3d1paN2xGbjlvVVd4YzU2YmZoZmtZUXJadkV6dFlyQmsvZEVEM2k1Y2pjaEs5aTc3NUoyRFZIMGpITkdsbUY3OU1wYXVtTkgrSTdnaWFRUkFYb0d5VEdTRkd3RlBPQXphU3IiLCJtYWMiOiI4YzkxYzIxOTE1ZjRhNzgzNWU5MGZmZWZhMzI1YzJjZDljNjgyMzZlNDVmNTY3YmIwZDMwOTliNzMyOTRiZGQxIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ind1RmZHWDhTQWFnVkg1enVFc0phc3c9PSIsInZhbHVlIjoidXkxL2RTMVJhYXNtaVhSNml2N2NnbUllUVdhU3dBN0xqdUoyWU5hcjFsL2tFTzNudWx2ZnpvRFpxVmN5OFB3M1cxZTgrUzdtOCtIVHd2UWlkNnpXYU5kSVh1c3VZcTB6WGJlSG1zZDZUUStwWnNmbnJxdFZMVTVPOStUd2JPaXUiLCJtYWMiOiIxZGVlODliZTYwZWNiOTY5ZjM5MDMxMTNiYzlkN2ZmZDBmYjlkNjEzY2ZkZjUyYzU5N2U5ZDZhODI0M2U0NjQ0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1752569633.1433</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1752569633</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-171935784\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1487908347 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">lWz8fwnB8ZvveFIAdF4XqrkKmMmueLxxZxRrzgVU</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1487908347\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1828571248 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Jul 2025 08:54:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImRDUy9seDNhcFM1QTdWRUVpQmIwK3c9PSIsInZhbHVlIjoiWmJRZWlpVDBrdjFxL0RaUnV4VFM0c2ZDdHVJeklOMnFqT2RBWGx1dGkvbGNnQ1c4MG4wRHhreGJHSFpMRk9OSDBBeGJ2OFo5aXlEY2s4UHNPTGNlZGs4NkE5WFNCMkRpRHZzM0dmcko0MU5NRk9SOG82VkVwbmlKaUVQSmIraGUiLCJtYWMiOiI1Y2M1MDlkN2I1ZmMyYmIzNTVmZTlhN2VlZjgzYmZhMzQ1YmEyYzBiOTBmNDkwMDRjNmU4NTE5YTdkZWVlZmEyIiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 10:54:02 GMT; Max-Age=7199; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Ik1iUGR1UTRzTnRGVG5mTEJYYVdrZkE9PSIsInZhbHVlIjoiczgyZHQxbFN0empOeFBDZ3NEVmxQODNVd2FrRFNNU1ZKN0pyTndTQjNuQmFNcUI2YWRwa1JrTkpFMGtBeXRPL2JrRE8wOUluNGlaSzJ4YlNsTC9pMjV3UXBtWnNUbmh5eHdiZTRBalhVbUUzbGVyVHowM3JDckJxODZVR3dOMmUiLCJtYWMiOiJiMmVjYWEwZTE5YjkwZGI4NDYzZGI3MzllYWM3NDE0YWM4MTUxNWZjZjU4OTFmMmQ3YTllYjFiYmIzNjM5MDU5IiwidGFnIjoiIn0%3D; expires=Tue, 15 Jul 2025 10:54:02 GMT; Max-Age=7199; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImRDUy9seDNhcFM1QTdWRUVpQmIwK3c9PSIsInZhbHVlIjoiWmJRZWlpVDBrdjFxL0RaUnV4VFM0c2ZDdHVJeklOMnFqT2RBWGx1dGkvbGNnQ1c4MG4wRHhreGJHSFpMRk9OSDBBeGJ2OFo5aXlEY2s4UHNPTGNlZGs4NkE5WFNCMkRpRHZzM0dmcko0MU5NRk9SOG82VkVwbmlKaUVQSmIraGUiLCJtYWMiOiI1Y2M1MDlkN2I1ZmMyYmIzNTVmZTlhN2VlZjgzYmZhMzQ1YmEyYzBiOTBmNDkwMDRjNmU4NTE5YTdkZWVlZmEyIiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 10:54:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Ik1iUGR1UTRzTnRGVG5mTEJYYVdrZkE9PSIsInZhbHVlIjoiczgyZHQxbFN0empOeFBDZ3NEVmxQODNVd2FrRFNNU1ZKN0pyTndTQjNuQmFNcUI2YWRwa1JrTkpFMGtBeXRPL2JrRE8wOUluNGlaSzJ4YlNsTC9pMjV3UXBtWnNUbmh5eHdiZTRBalhVbUUzbGVyVHowM3JDckJxODZVR3dOMmUiLCJtYWMiOiJiMmVjYWEwZTE5YjkwZGI4NDYzZGI3MzllYWM3NDE0YWM4MTUxNWZjZjU4OTFmMmQ3YTllYjFiYmIzNjM5MDU5IiwidGFnIjoiIn0%3D; expires=Tue, 15-Jul-2025 10:54:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1828571248\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1426559873 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">HDK0mRbkDvv2cdX03ve8fTJp3eXq8ixJSlVRbhnW</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">http://127.0.0.1:8000/pedagogiques/resultat</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752562641</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1426559873\", {\"maxDepth\":0})</script>\n"}}